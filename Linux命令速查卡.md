# Linux命令速查卡

> 2025年8月版 - 最常用Linux命令快速参考

## 🗂️ 文件操作
| 命令 | 功能 | 常用参数 |
|------|------|----------|
| `ls` | 列出文件 | `-la` (详细+隐藏), `-lh` (人类可读) |
| `cd` | 切换目录 | `~` (主目录), `-` (上个目录), `..` (上级) |
| `pwd` | 当前路径 | |
| `mkdir` | 创建目录 | `-p` (递归创建) |
| `rm` | 删除 | `-rf` (强制递归), `-i` (确认) |
| `cp` | 复制 | `-r` (递归), `-p` (保持属性) |
| `mv` | 移动/重命名 | |
| `find` | 查找文件 | `-name "*.txt"`, `-type f`, `-size +1M` |
| `locate` | 快速查找 | `updatedb` (更新数据库) |
| `ln` | 创建链接 | `-s` (软链接) |

## 📄 文件内容
| 命令 | 功能 | 常用参数 |
|------|------|----------|
| `cat` | 显示文件 | `-n` (显示行号) |
| `less` | 分页查看 | `q` (退出), `/` (搜索) |
| `head` | 显示开头 | `-n 20` (前20行) |
| `tail` | 显示末尾 | `-f` (实时监控), `-n 20` |
| `grep` | 文本搜索 | `-r` (递归), `-i` (忽略大小写), `-v` (反选) |
| `awk` | 文本处理 | `'{print $1}'` (打印第1列) |
| `sed` | 流编辑 | `'s/old/new/g'` (替换) |
| `sort` | 排序 | `-n` (数字), `-r` (逆序) |
| `uniq` | 去重 | `-c` (计数) |
| `wc` | 统计 | `-l` (行数), `-w` (词数) |

## 🔐 权限管理
| 命令 | 功能 | 示例 |
|------|------|------|
| `chmod` | 修改权限 | `755`, `+x`, `u+w,g-r` |
| `chown` | 修改所有者 | `user:group file` |
| `chgrp` | 修改组 | `group file` |
| `umask` | 默认权限 | `022` |

**权限数字对照：**
- `7` = rwx (读写执行)
- `6` = rw- (读写)
- `5` = r-x (读执行)
- `4` = r-- (只读)

## 🖥️ 系统信息
| 命令 | 功能 | 常用参数 |
|------|------|----------|
| `ps` | 进程信息 | `aux` (所有进程), `-ef` |
| `top` | 实时进程 | `q` (退出), `k` (杀进程) |
| `htop` | 增强版top | 彩色交互式 |
| `free` | 内存信息 | `-h` (人类可读) |
| `df` | 磁盘空间 | `-h` (人类可读) |
| `du` | 目录大小 | `-sh` (总大小) |
| `uptime` | 运行时间 | |
| `uname` | 系统信息 | `-a` (所有信息) |
| `whoami` | 当前用户 | |
| `date` | 日期时间 | `'+%Y-%m-%d %H:%M:%S'` |

## ⚡ 进程管理
| 命令 | 功能 | 示例 |
|------|------|------|
| `kill` | 终止进程 | `kill -9 PID` (强制) |
| `killall` | 按名称终止 | `killall firefox` |
| `pkill` | 按条件终止 | `pkill -f pattern` |
| `pgrep` | 查找进程 | `pgrep nginx` |
| `nohup` | 后台运行 | `nohup command &` |
| `jobs` | 作业列表 | |
| `bg` | 后台作业 | `bg %1` |
| `fg` | 前台作业 | `fg %1` |

## 🌐 网络操作
| 命令 | 功能 | 常用参数 |
|------|------|----------|
| `ping` | 测试连通性 | `-c 4` (发送4个包) |
| `wget` | 下载文件 | `-c` (断点续传), `-r` (递归) |
| `curl` | 数据传输 | `-O` (下载), `-X POST` |
| `ssh` | 远程登录 | `-p 2222` (指定端口) |
| `scp` | 远程复制 | `file user@host:/path` |
| `rsync` | 同步文件 | `-av` (归档+详细) |
| `netstat` | 网络连接 | `-tuln` (监听端口) |
| `ss` | 现代netstat | `-tuln` |

## 📦 压缩归档
| 命令 | 功能 | 示例 |
|------|------|------|
| `tar` | 归档 | `-czf` (创建gzip), `-xzf` (解压gzip) |
| `gzip` | 压缩 | `gzip file` |
| `gunzip` | 解压 | `gunzip file.gz` |
| `zip` | 创建zip | `zip -r archive.zip dir/` |
| `unzip` | 解压zip | `unzip archive.zip` |

**tar常用组合：**
- `tar -czf archive.tar.gz files/` (创建)
- `tar -xzf archive.tar.gz` (解压)
- `tar -tf archive.tar.gz` (查看内容)

## 👥 用户管理
| 命令 | 功能 | 示例 |
|------|------|------|
| `su` | 切换用户 | `su -` (切换到root) |
| `sudo` | 提权执行 | `sudo command` |
| `passwd` | 修改密码 | `passwd username` |
| `useradd` | 添加用户 | `-m` (创建主目录) |
| `userdel` | 删除用户 | `-r` (删除主目录) |
| `groups` | 查看用户组 | |

## 🔧 系统服务
| 命令 | 功能 | 示例 |
|------|------|------|
| `systemctl` | 服务管理 | `start/stop/restart/status service` |
| `service` | 传统服务 | `service nginx start` |
| `crontab` | 定时任务 | `-l` (查看), `-e` (编辑) |
| `mount` | 挂载 | `mount /dev/sdb1 /mnt` |
| `umount` | 卸载 | `umount /mnt` |

## 🔍 常用组合命令

### 查找和处理
```bash
# 查找大文件
find /path -size +100M -exec ls -lh {} \;

# 统计代码行数
find . -name "*.py" | xargs wc -l

# 批量重命名
for f in *.txt; do mv "$f" "${f%.txt}.bak"; done
```

### 日志分析
```bash
# 统计IP访问次数
awk '{print $1}' access.log | sort | uniq -c | sort -nr

# 实时监控错误日志
tail -f error.log | grep --color=always ERROR

# 查找特定时间段日志
sed -n '/2025-08-01 10:00/,/2025-08-01 11:00/p' app.log
```

### 系统监控
```bash
# 查看占用CPU最多的进程
ps aux --sort=-%cpu | head -10

# 监控磁盘使用率
df -h | grep -v tmpfs | sort -k5 -nr

# 实时网络连接数
watch -n 1 'netstat -an | grep ESTABLISHED | wc -l'
```

## 📝 快捷键

### 命令行编辑
- `Ctrl+A` - 行首
- `Ctrl+E` - 行尾
- `Ctrl+U` - 删除到行首
- `Ctrl+K` - 删除到行尾
- `Ctrl+W` - 删除前一个单词
- `Ctrl+L` - 清屏
- `Ctrl+R` - 搜索历史命令

### 进程控制
- `Ctrl+C` - 终止当前进程
- `Ctrl+Z` - 暂停当前进程
- `Ctrl+D` - 退出当前shell

## 🎯 实用技巧

### 历史命令
- `!!` - 执行上一条命令
- `!n` - 执行历史第n条命令
- `!string` - 执行最近以string开头的命令
- `^old^new` - 替换上条命令中的old为new

### 文件操作技巧
```bash
# 快速备份文件
cp file{,.bak}

# 创建目录并进入
mkdir -p /path/to/dir && cd $_

# 比较两个目录
diff -r dir1/ dir2/

# 安全删除
shred -vfz -n 3 sensitive_file
```

### 别名设置
```bash
alias ll='ls -la'
alias la='ls -A'
alias l='ls -CF'
alias ..='cd ..'
alias ...='cd ../..'
alias grep='grep --color=auto'
```

---

## 🚨 注意事项

⚠️ **危险命令警告：**
- `rm -rf /` - 删除根目录（极其危险）
- `dd if=/dev/zero of=/dev/sda` - 清空硬盘
- `:(){ :|:& };:` - fork炸弹

✅ **安全建议：**
- 使用 `rm -i` 进行交互式删除
- 重要操作前先备份
- 使用 `sudo` 而不是直接root登录
- 定期更新系统和软件包

---

*快速参考卡 - 2025年8月版*
*适用于：Ubuntu, CentOS, RHEL, Debian等主流Linux发行版*
