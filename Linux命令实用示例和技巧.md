# Linux命令实用示例和技巧

> 本文档提供Linux常用命令的实用示例、组合技巧和最佳实践

## 目录
- [文件操作高级技巧](#文件操作高级技巧)
- [文本处理组合命令](#文本处理组合命令)
- [系统监控实用技巧](#系统监控实用技巧)
- [网络诊断和管理](#网络诊断和管理)
- [日常运维脚本](#日常运维脚本)

---

## 文件操作高级技巧

### 批量文件操作
```bash
# 批量重命名文件
for file in *.txt; do mv "$file" "${file%.txt}.bak"; done

# 查找并删除空文件
find /path -type f -empty -delete

# 查找大文件（大于100M）
find /path -type f -size +100M -exec ls -lh {} \;

# 按文件扩展名统计数量
find /path -type f | sed 's/.*\.//' | sort | uniq -c | sort -nr

# 查找重复文件
find /path -type f -exec md5sum {} \; | sort | uniq -d -w32
```

### 文件权限批量操作
```bash
# 批量设置目录权限为755，文件权限为644
find /path -type d -exec chmod 755 {} \;
find /path -type f -exec chmod 644 {} \;

# 查找具有特定权限的文件
find /path -perm 777 -type f

# 查找SUID文件
find /path -perm -4000 -type f

# 查找所有者为特定用户的文件
find /path -user username -type f
```

### 高效文件同步
```bash
# 本地目录同步（保持权限和时间戳）
rsync -av --progress source/ destination/

# 远程同步并排除特定文件
rsync -av --exclude='*.log' --exclude='tmp/' source/ user@host:/path/

# 增量备份
rsync -av --link-dest=/backup/last /source/ /backup/current/

# 同步时显示传输速度
rsync -av --progress --stats source/ destination/
```

---

## 文本处理组合命令

### 日志分析常用组合
```bash
# 统计访问最多的IP地址
awk '{print $1}' access.log | sort | uniq -c | sort -nr | head -10

# 分析HTTP状态码分布
awk '{print $9}' access.log | sort | uniq -c | sort -nr

# 查找特定时间段的日志
sed -n '/2025-08-01 10:00/,/2025-08-01 11:00/p' app.log

# 实时监控日志中的错误
tail -f app.log | grep --color=always -i error

# 统计日志文件中各级别日志数量
grep -o '\[ERROR\]\|\[WARN\]\|\[INFO\]' app.log | sort | uniq -c
```

### 文本数据处理
```bash
# CSV文件处理：提取特定列
awk -F',' '{print $2,$5}' data.csv

# 去除文件中的空行和注释行
grep -v '^#' file.txt | grep -v '^$'

# 统计文件中单词频率
tr -s ' ' '\n' < file.txt | sort | uniq -c | sort -nr

# 合并多个文件并去重
cat file1.txt file2.txt | sort | uniq > merged.txt

# 比较两个文件的差异
diff -u file1.txt file2.txt
comm -23 <(sort file1.txt) <(sort file2.txt)  # 只在file1中的行
```

### 高级文本搜索
```bash
# 递归搜索包含特定模式的文件
grep -r "pattern" /path --include="*.py" --exclude-dir=".git"

# 搜索二进制文件中的字符串
strings binary_file | grep "pattern"

# 使用正则表达式搜索
grep -E "^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$" file.txt

# 搜索并显示上下文
grep -C 3 "pattern" file.txt  # 显示匹配行前后3行

# 多模式搜索
grep -E "pattern1|pattern2|pattern3" file.txt
```

---

## 系统监控实用技巧

### 进程监控和管理
```bash
# 查找占用CPU最多的进程
ps aux --sort=-%cpu | head -10

# 查找占用内存最多的进程
ps aux --sort=-%mem | head -10

# 监控特定进程的资源使用
watch -n 1 'ps -p PID -o pid,ppid,cmd,%mem,%cpu'

# 查找僵尸进程
ps aux | awk '$8 ~ /^Z/ { print $2 }'

# 按进程名查找并显示详细信息
pgrep -f process_name | xargs ps -fp
```

### 系统资源监控
```bash
# 实时监控磁盘I/O
iostat -x 1

# 监控网络流量
iftop -i eth0

# 查看系统负载历史
sar -u 1 10  # 每秒显示CPU使用率，共10次

# 监控内存使用情况
watch -n 1 'free -h && echo && ps aux --sort=-%mem | head -10'

# 查看文件系统使用情况
df -h | grep -v tmpfs | sort -k5 -nr
```

### 性能分析
```bash
# 分析系统调用
strace -c command  # 统计系统调用
strace -p PID      # 跟踪运行中的进程

# 分析库调用
ltrace command

# 查看进程打开的文件
lsof -p PID

# 查看端口占用情况
lsof -i :port_number
netstat -tulpn | grep :port_number
```

---

## 网络诊断和管理

### 网络连通性测试
```bash
# 测试网络延迟和丢包
ping -c 10 -i 0.5 hostname

# 路由跟踪
traceroute hostname
mtr hostname  # 持续路由跟踪

# 测试端口连通性
telnet hostname port
nc -zv hostname port

# 批量ping测试
for ip in 192.168.1.{1..254}; do
    ping -c 1 -W 1 $ip > /dev/null && echo "$ip is up"
done
```

### 网络配置和管理
```bash
# 查看网络接口统计
cat /proc/net/dev

# 临时配置IP地址
ip addr add *************/24 dev eth0
ip route add default via ***********

# 查看ARP表
arp -a
ip neigh show

# 监控网络连接
watch -n 1 'netstat -an | grep ESTABLISHED | wc -l'

# 查看网络服务
ss -tulpn | grep LISTEN
```

### 防火墙和安全
```bash
# 查看当前防火墙规则
iptables -L -n -v

# 简单端口开放
iptables -A INPUT -p tcp --dport 80 -j ACCEPT
iptables -A INPUT -p tcp --dport 443 -j ACCEPT

# 阻止特定IP
iptables -A INPUT -s ************* -j DROP

# 查看失败的SSH登录尝试
grep "Failed password" /var/log/auth.log | tail -10

# 查看当前登录用户
w
last | head -10
```

---

## 日常运维脚本

### 系统清理脚本
```bash
#!/bin/bash
# 系统清理脚本

echo "开始系统清理..."

# 清理临时文件
find /tmp -type f -atime +7 -delete
echo "已清理/tmp目录中7天前的文件"

# 清理日志文件
find /var/log -name "*.log" -size +100M -exec truncate -s 0 {} \;
echo "已清理大于100M的日志文件"

# 清理包管理器缓存
apt-get clean 2>/dev/null || yum clean all 2>/dev/null
echo "已清理包管理器缓存"

# 显示磁盘使用情况
echo "当前磁盘使用情况："
df -h | grep -v tmpfs
```

### 备份脚本
```bash
#!/bin/bash
# 自动备份脚本

BACKUP_DIR="/backup"
SOURCE_DIR="/important/data"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="backup_$DATE.tar.gz"

# 创建备份
tar -czf "$BACKUP_DIR/$BACKUP_FILE" "$SOURCE_DIR"

# 删除7天前的备份
find "$BACKUP_DIR" -name "backup_*.tar.gz" -mtime +7 -delete

echo "备份完成：$BACKUP_DIR/$BACKUP_FILE"
```

### 服务监控脚本
```bash
#!/bin/bash
# 服务监控脚本

SERVICES=("nginx" "mysql" "redis")

for service in "${SERVICES[@]}"; do
    if systemctl is-active --quiet "$service"; then
        echo "✓ $service 运行正常"
    else
        echo "✗ $service 未运行，尝试启动..."
        systemctl start "$service"
        if systemctl is-active --quiet "$service"; then
            echo "✓ $service 启动成功"
        else
            echo "✗ $service 启动失败"
        fi
    fi
done
```

### 磁盘空间监控
```bash
#!/bin/bash
# 磁盘空间监控脚本

THRESHOLD=80

df -h | grep -vE '^Filesystem|tmpfs|cdrom' | awk '{ print $5 " " $1 }' | while read output;
do
    usage=$(echo $output | awk '{ print $1}' | cut -d'%' -f1)
    partition=$(echo $output | awk '{ print $2 }')
    
    if [ $usage -ge $THRESHOLD ]; then
        echo "警告：分区 $partition 使用率达到 $usage%"
        # 这里可以添加邮件通知或其他告警机制
    fi
done
```

---

## 实用技巧和最佳实践

### 命令行效率技巧
```bash
# 使用历史命令
!!          # 执行上一条命令
!n          # 执行历史中第n条命令
!string     # 执行最近以string开头的命令
^old^new    # 替换上一条命令中的old为new

# 快速目录切换
cd -        # 切换到上一个目录
pushd /path && popd  # 目录栈操作

# 命令别名
alias ll='ls -la'
alias grep='grep --color=auto'
alias ..='cd ..'
```

### 安全最佳实践
```bash
# 安全删除文件（覆盖数据）
shred -vfz -n 3 sensitive_file

# 检查文件完整性
md5sum file > file.md5
md5sum -c file.md5

# 安全传输文件
scp -C file user@host:/path  # 压缩传输
rsync -av --progress file user@host:/path

# 查看登录历史
last -n 20
lastlog
```

### 性能优化技巧
```bash
# 使用并行处理
find /path -name "*.txt" | xargs -P 4 -I {} process_file {}

# 大文件处理
split -l 1000 large_file.txt part_  # 按行分割
split -b 100M large_file.bin part_  # 按大小分割

# 内存映射文件操作
mmap large_file  # 对于大文件操作更高效
```

---

*文档更新时间：2025年8月*
*适用场景：Linux系统管理、运维自动化、日常开发*
