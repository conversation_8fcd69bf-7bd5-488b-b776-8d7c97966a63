# 2025年8月全网最常用的Linux命令100条

> 本文档整理了2025年8月最常用的Linux命令，按功能分类，适合初学者和进阶用户参考。

## 目录
- [文件和目录操作 (20个)](#文件和目录操作)
- [文件内容查看和编辑 (15个)](#文件内容查看和编辑)
- [文件权限和所有权 (8个)](#文件权限和所有权)
- [系统信息和监控 (15个)](#系统信息和监控)
- [进程管理 (10个)](#进程管理)
- [网络操作 (12个)](#网络操作)
- [压缩和归档 (8个)](#压缩和归档)
- [用户和组管理 (7个)](#用户和组管理)
- [系统服务和启动 (5个)](#系统服务和启动)

---

## 文件和目录操作

### 1. `ls` - 列出目录内容
```bash
ls -la          # 详细列出所有文件（包括隐藏文件）
ls -lh          # 以人类可读格式显示文件大小
ls -lt          # 按修改时间排序
```

### 2. `cd` - 切换目录
```bash
cd /path/to/dir # 切换到指定目录
cd ~            # 切换到用户主目录
cd -            # 切换到上一个目录
cd ..           # 切换到上级目录
```

### 3. `pwd` - 显示当前工作目录
```bash
pwd             # 显示当前完整路径
```

### 4. `mkdir` - 创建目录
```bash
mkdir dirname   # 创建单个目录
mkdir -p a/b/c  # 递归创建多级目录
mkdir -m 755 dir # 创建目录并设置权限
```

### 5. `rmdir` - 删除空目录
```bash
rmdir dirname   # 删除空目录
rmdir -p a/b/c  # 递归删除空目录
```

### 6. `rm` - 删除文件和目录
```bash
rm filename     # 删除文件
rm -r dirname   # 递归删除目录
rm -f filename  # 强制删除文件
rm -rf dirname  # 强制递归删除目录
```

### 7. `cp` - 复制文件和目录
```bash
cp file1 file2  # 复制文件
cp -r dir1 dir2 # 递归复制目录
cp -p file1 file2 # 保持文件属性复制
```

### 8. `mv` - 移动/重命名文件和目录
```bash
mv file1 file2  # 重命名文件
mv file1 /path/ # 移动文件到指定目录
mv dir1 dir2    # 重命名目录
```

### 9. `find` - 查找文件和目录
```bash
find /path -name "*.txt"    # 按名称查找
find /path -type f -size +1M # 查找大于1M的文件
find /path -mtime -7        # 查找7天内修改的文件
```

### 10. `locate` - 快速查找文件
```bash
locate filename # 快速查找文件位置
updatedb        # 更新locate数据库
```

### 11. `which` - 查找命令位置
```bash
which python    # 查找python命令的位置
which -a python # 查找所有python命令位置
```

### 12. `whereis` - 查找命令、源码、手册位置
```bash
whereis python  # 查找python相关文件位置
```

### 13. `ln` - 创建链接
```bash
ln file1 file2     # 创建硬链接
ln -s file1 file2  # 创建软链接（符号链接）
```

### 14. `tree` - 以树形结构显示目录
```bash
tree            # 显示当前目录树
tree -L 2       # 只显示2级目录
tree -a         # 显示所有文件（包括隐藏文件）
```

### 15. `du` - 显示目录空间使用情况
```bash
du -h           # 以人类可读格式显示
du -sh *        # 显示当前目录下各文件/目录大小
du -sh /path    # 显示指定目录总大小
```

### 16. `df` - 显示文件系统磁盘空间使用情况
```bash
df -h           # 以人类可读格式显示
df -T           # 显示文件系统类型
```

### 17. `touch` - 创建空文件或更新文件时间戳
```bash
touch filename  # 创建空文件或更新时间戳
touch -t 202501010000 file # 设置特定时间戳
```

### 18. `stat` - 显示文件详细信息
```bash
stat filename   # 显示文件的详细状态信息
```

### 19. `file` - 确定文件类型
```bash
file filename   # 显示文件类型
file *          # 显示当前目录所有文件类型
```

### 20. `basename` / `dirname` - 提取文件名/目录名
```bash
basename /path/to/file.txt  # 输出: file.txt
dirname /path/to/file.txt   # 输出: /path/to
```

---

## 文件内容查看和编辑

### 21. `cat` - 显示文件内容
```bash
cat filename    # 显示文件内容
cat -n filename # 显示行号
cat file1 file2 # 连接显示多个文件
```

### 22. `less` - 分页查看文件内容
```bash
less filename   # 分页查看文件
less +F filename # 实时查看文件末尾（类似tail -f）
```

### 23. `more` - 分页查看文件内容
```bash
more filename   # 分页查看文件（只能向前翻页）
```

### 24. `head` - 显示文件开头
```bash
head filename   # 显示前10行
head -n 20 file # 显示前20行
```

### 25. `tail` - 显示文件末尾
```bash
tail filename   # 显示后10行
tail -n 20 file # 显示后20行
tail -f filename # 实时监控文件变化
```

### 26. `grep` - 文本搜索
```bash
grep "pattern" file     # 搜索包含pattern的行
grep -r "pattern" dir   # 递归搜索目录
grep -i "pattern" file  # 忽略大小写搜索
grep -v "pattern" file  # 显示不包含pattern的行
```

### 27. `awk` - 文本处理工具
```bash
awk '{print $1}' file   # 打印第一列
awk -F: '{print $1}' /etc/passwd # 以:为分隔符打印第一列
```

### 28. `sed` - 流编辑器
```bash
sed 's/old/new/g' file  # 替换文本
sed -n '1,10p' file     # 打印1-10行
sed -i 's/old/new/g' file # 直接修改文件
```

### 29. `sort` - 排序文本
```bash
sort filename   # 按字母顺序排序
sort -n file    # 按数字排序
sort -r file    # 逆序排序
```

### 30. `uniq` - 去除重复行
```bash
uniq filename   # 去除相邻重复行
sort file | uniq # 去除所有重复行
uniq -c file    # 统计重复次数
```

### 31. `wc` - 统计文件信息
```bash
wc filename     # 显示行数、词数、字符数
wc -l filename  # 只显示行数
wc -w filename  # 只显示词数
```

### 32. `cut` - 提取文本列
```bash
cut -d: -f1 /etc/passwd # 以:为分隔符提取第1列
cut -c1-10 file         # 提取每行的1-10个字符
```

### 33. `tr` - 字符转换
```bash
tr 'a-z' 'A-Z' < file   # 小写转大写
tr -d ' ' < file        # 删除空格
```

### 34. `vim` / `vi` - 文本编辑器
```bash
vim filename    # 使用vim编辑文件
vi filename     # 使用vi编辑文件
```

### 35. `nano` - 简单文本编辑器
```bash
nano filename   # 使用nano编辑文件
```

---

## 文件权限和所有权

### 36. `chmod` - 修改文件权限
```bash
chmod 755 filename      # 设置权限为rwxr-xr-x
chmod +x filename       # 添加执行权限
chmod -w filename       # 移除写权限
chmod u+x,g-w,o-r file  # 复杂权限设置
```

### 37. `chown` - 修改文件所有者
```bash
chown user filename     # 修改文件所有者
chown user:group file   # 修改所有者和组
chown -R user:group dir # 递归修改目录
```

### 38. `chgrp` - 修改文件所属组
```bash
chgrp group filename    # 修改文件所属组
chgrp -R group dirname  # 递归修改目录所属组
```

### 39. `umask` - 设置默认权限
```bash
umask           # 查看当前umask值
umask 022       # 设置umask值
```

### 40. `lsattr` - 显示文件扩展属性
```bash
lsattr filename # 显示文件扩展属性
```

### 41. `chattr` - 修改文件扩展属性
```bash
chattr +i filename # 设置文件为不可修改
chattr -i filename # 移除不可修改属性
```

### 42. `getfacl` - 显示文件ACL权限
```bash
getfacl filename # 显示文件的ACL权限
```

### 43. `setfacl` - 设置文件ACL权限
```bash
setfacl -m u:user:rwx filename # 设置用户ACL权限
```

---

## 系统信息和监控

### 44. `ps` - 显示进程信息
```bash
ps aux          # 显示所有进程详细信息
ps -ef          # 显示所有进程
ps -u username  # 显示指定用户的进程
```

### 45. `top` - 实时显示进程信息
```bash
top             # 实时显示进程信息
top -u username # 显示指定用户的进程
```

### 46. `htop` - 增强版top
```bash
htop            # 彩色交互式进程查看器
```

### 47. `free` - 显示内存使用情况
```bash
free -h         # 以人类可读格式显示内存信息
free -m         # 以MB为单位显示
```

### 48. `uptime` - 显示系统运行时间
```bash
uptime          # 显示系统运行时间和负载
```

### 49. `uname` - 显示系统信息
```bash
uname -a        # 显示所有系统信息
uname -r        # 显示内核版本
```

### 50. `whoami` - 显示当前用户
```bash
whoami          # 显示当前登录用户名
```

### 51. `who` - 显示登录用户
```bash
who             # 显示当前登录的所有用户
```

### 52. `w` - 显示登录用户及其活动
```bash
w               # 显示登录用户及其正在执行的命令
```

### 53. `id` - 显示用户和组ID
```bash
id              # 显示当前用户的UID和GID
id username     # 显示指定用户的ID信息
```

### 54. `date` - 显示或设置系统日期
```bash
date            # 显示当前日期时间
date '+%Y-%m-%d %H:%M:%S' # 格式化显示日期
```

### 55. `cal` - 显示日历
```bash
cal             # 显示当前月份日历
cal 2025        # 显示2025年日历
```

### 56. `history` - 显示命令历史
```bash
history         # 显示命令历史
history | grep command # 搜索历史命令
```

### 57. `lscpu` - 显示CPU信息
```bash
lscpu           # 显示CPU详细信息
```

### 58. `lsblk` - 显示块设备信息
```bash
lsblk           # 以树形结构显示块设备
```

---

## 进程管理

### 59. `kill` - 终止进程
```bash
kill PID        # 终止指定PID的进程
kill -9 PID     # 强制终止进程
kill -TERM PID  # 发送TERM信号
```

### 60. `killall` - 按名称终止进程
```bash
killall process_name # 终止所有同名进程
killall -9 process   # 强制终止所有同名进程
```

### 61. `pkill` - 按条件终止进程
```bash
pkill -f pattern     # 按命令行模式终止进程
pkill -u username    # 终止指定用户的所有进程
```

### 62. `pgrep` - 按条件查找进程
```bash
pgrep process_name   # 查找进程PID
pgrep -u username    # 查找指定用户的进程
```

### 63. `nohup` - 后台运行命令
```bash
nohup command &      # 后台运行命令，忽略挂起信号
```

### 64. `jobs` - 显示作业列表
```bash
jobs            # 显示当前shell的作业列表
```

### 65. `bg` - 将作业放到后台
```bash
bg %1           # 将作业1放到后台运行
```

### 66. `fg` - 将作业调到前台
```bash
fg %1           # 将作业1调到前台运行
```

### 67. `screen` - 终端复用器
```bash
screen          # 启动新的screen会话
screen -r       # 恢复screen会话
```

### 68. `tmux` - 终端复用器
```bash
tmux            # 启动新的tmux会话
tmux attach     # 附加到tmux会话
```

---

## 网络操作

### 69. `ping` - 测试网络连通性
```bash
ping hostname   # 测试到主机的连通性
ping -c 4 host  # 发送4个包后停止
```

### 70. `wget` - 下载文件
```bash
wget URL        # 下载文件
wget -c URL     # 断点续传下载
wget -r URL     # 递归下载
```

### 71. `curl` - 传输数据
```bash
curl URL        # 获取URL内容
curl -O URL     # 下载文件
curl -X POST URL # 发送POST请求
```

### 72. `ssh` - 远程登录
```bash
ssh user@host   # SSH登录远程主机
ssh -p 2222 user@host # 指定端口登录
```

### 73. `scp` - 远程复制文件
```bash
scp file user@host:/path # 复制文件到远程主机
scp user@host:/path/file . # 从远程主机复制文件
```

### 74. `rsync` - 同步文件
```bash
rsync -av source/ dest/  # 同步目录
rsync -av user@host:/path/ . # 从远程同步
```

### 75. `netstat` - 显示网络连接
```bash
netstat -tuln   # 显示监听端口
netstat -an     # 显示所有网络连接
```

### 76. `ss` - 现代版netstat
```bash
ss -tuln        # 显示监听端口
ss -an          # 显示所有连接
```

### 77. `iptables` - 防火墙配置
```bash
iptables -L     # 查看防火墙规则
iptables -A INPUT -p tcp --dport 80 -j ACCEPT # 允许80端口
```

### 78. `ifconfig` - 网络接口配置
```bash
ifconfig        # 显示网络接口信息
ifconfig eth0 up # 启用网络接口
```

### 79. `ip` - 现代网络配置工具
```bash
ip addr show    # 显示IP地址
ip route show   # 显示路由表
```

### 80. `nslookup` - DNS查询
```bash
nslookup domain # DNS查询域名
```

---

## 压缩和归档

### 81. `tar` - 归档文件
```bash
tar -czf archive.tar.gz files # 创建gzip压缩归档
tar -xzf archive.tar.gz       # 解压gzip归档
tar -tf archive.tar           # 查看归档内容
```

### 82. `gzip` - 压缩文件
```bash
gzip filename   # 压缩文件
gzip -d file.gz # 解压文件
```

### 83. `gunzip` - 解压gzip文件
```bash
gunzip file.gz  # 解压gzip文件
```

### 84. `zip` - 创建zip归档
```bash
zip archive.zip files # 创建zip归档
zip -r archive.zip dir # 递归压缩目录
```

### 85. `unzip` - 解压zip文件
```bash
unzip archive.zip     # 解压zip文件
unzip -l archive.zip  # 查看zip内容
```

### 86. `7z` - 7zip压缩工具
```bash
7z a archive.7z files # 创建7z归档
7z x archive.7z       # 解压7z文件
```

### 87. `rar` - RAR压缩工具
```bash
rar a archive.rar files # 创建rar归档
```

### 88. `unrar` - 解压RAR文件
```bash
unrar x archive.rar   # 解压rar文件
```

---

## 用户和组管理

### 89. `su` - 切换用户
```bash
su username     # 切换到指定用户
su -            # 切换到root用户
```

### 90. `sudo` - 以其他用户身份执行命令
```bash
sudo command    # 以root身份执行命令
sudo -u user command # 以指定用户身份执行
```

### 91. `useradd` - 添加用户
```bash
useradd username      # 添加新用户
useradd -m username   # 添加用户并创建主目录
```

### 92. `userdel` - 删除用户
```bash
userdel username      # 删除用户
userdel -r username   # 删除用户及其主目录
```

### 93. `usermod` - 修改用户
```bash
usermod -aG group user # 将用户添加到组
usermod -s /bin/bash user # 修改用户shell
```

### 94. `passwd` - 修改密码
```bash
passwd          # 修改当前用户密码
passwd username # 修改指定用户密码
```

### 95. `groups` - 显示用户所属组
```bash
groups          # 显示当前用户所属组
groups username # 显示指定用户所属组
```

---

## 系统服务和启动

### 96. `systemctl` - 系统服务管理
```bash
systemctl status service  # 查看服务状态
systemctl start service   # 启动服务
systemctl stop service    # 停止服务
systemctl enable service  # 设置服务开机启动
```

### 97. `service` - 传统服务管理
```bash
service servicename start   # 启动服务
service servicename status  # 查看服务状态
```

### 98. `crontab` - 定时任务
```bash
crontab -l      # 查看当前用户的定时任务
crontab -e      # 编辑定时任务
```

### 99. `mount` - 挂载文件系统
```bash
mount /dev/sdb1 /mnt # 挂载设备到目录
mount -t ext4 device /mnt # 指定文件系统类型挂载
```

### 100. `umount` - 卸载文件系统
```bash
umount /mnt     # 卸载挂载点
umount /dev/sdb1 # 卸载设备
```

---

## 总结

这100个Linux命令涵盖了日常系统管理和开发工作中最常用的功能：

1. **文件操作** - 文件和目录的基本操作
2. **文本处理** - 查看、编辑、搜索文本内容
3. **权限管理** - 文件权限和所有权控制
4. **系统监控** - 系统状态和资源监控
5. **进程管理** - 进程控制和作业管理
6. **网络操作** - 网络连接和数据传输
7. **压缩归档** - 文件压缩和归档操作
8. **用户管理** - 用户和组的管理
9. **系统服务** - 服务管理和系统配置

掌握这些命令将大大提高在Linux环境下的工作效率。建议初学者从基础的文件操作命令开始，逐步学习更高级的系统管理命令。

---

*文档创建时间：2025年8月*
*适用系统：主流Linux发行版（Ubuntu、CentOS、RHEL、Debian等）*
